<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>Civitai Pyweb Hello</title>
        <link href="tailwind.css" rel="stylesheet">
        <style>
        /* 面板切换样式 */
        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* 动态卡片样式优化 */
        .card.h-auto {
            min-height: fit-content;
            transition: all 0.3s ease;
        }

        .card.h-auto:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        /* 图片网格自适应 */
        .grid-cols-4 > .card.h-auto {
            display: flex;
            flex-direction: column;
            height: auto;
        }

        .grid-cols-4 > .card.h-auto .card-body {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* 响应式图片容器 */
        .card figure.h-auto {
            aspect-ratio: 1;
            overflow: hidden;
        }

        .card figure.h-auto img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 文本内容自适应 */
        .card .min-h-8 {
            line-height: 1.2;
            word-wrap: break-word;
        }

        /* 统计卡片动态高度 */
        .stats.stats-horizontal {
            height: auto;
            min-height: fit-content;
        }

        .stats .stat {
            padding: 1rem;
            min-height: fit-content;
        }
        </style>
    </head>
    <body class="bg-base-200">
        <div class="flex h-screen">
            <!-- 侧边栏 -->
            <aside class="w-44 bg-base-100 shadow flex flex-col items-center py-6">
                <div class="text-2xl font-bold mb-8">Civitai Pyweb</div>
                <button class="btn btn-primary btn-block mb-2" id="btnSettings">
                    程序设置
</button>
                <button class="btn btn-ghost btn-block mb-2" id="btnImages">
                    图片展示
</button>
                <button class="btn btn-ghost btn-block mb-4" id="btnMeta">
                    元数据管理
</button>
                <div class="flex-grow"></div>
                <!-- 明暗主题切换开关 -->
                <div class="w-full px-2 mb-4">
                    <label class="flex cursor-pointer gap-2 items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun">
                            <circle cx="12" cy="12" r="4"/>
                            <path d="M12 2v2"/>
                            <path d="M12 20v2"/>
                            <path d="m4.93 4.93 1.41 1.41"/>
                            <path d="m17.66 17.66 1.41 1.41"/>
                            <path d="M2 12h2"/>
                            <path d="M20 12h2"/>
                            <path d="m6.34 17.66-1.41 1.41"/>
                            <path d="m19.07 4.93-1.41 1.41"/>
                        </svg>
                        <input type="checkbox" id="themeToggle" class="toggle toggle-sm" onchange="toggleDarkMode(this)"/>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon">
                            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
                        </svg>
                    </label>
                </div>
            </aside>
            <!-- 主内容区 -->
            <section class="flex-1 p-12 overflow-y-auto">
                <!-- 程序设置面板 -->
                <div id="settingsPanel" class="tab-pane active">
                    <!-- 页面标题区域 -->
                    <div class="bg-gradient-to-r from-primary/10 to-secondary/10 mb-16">
                        <div class="max-w-3xl mx-auto py-16 px-8 text-center">
                            <h1 class="text-4xl font-bold text-primary mb-4">程序设置</h1>
                            <p class="text-lg text-base-content/70">配置应用程序的基本设置和个性化选项</p>
                        </div>
                    </div>
                    <!-- API 配置卡片 -->
                    <div class="card bg-base-100 shadow-lg border border-base-300 h-auto max-w-3xl ml-auto mr-20 mx-8 mb-12">
                        <div class="card-body p-8">
                                <h2 class="card-title text-lg"> 
                            API 配置 </h2>
                                <p class="text-base-content/70 text-sm mb-3">配置 Civitai API 密钥以访问在线服务</p>
                                <form id="settingsForm">
                                    <div class="form-control">
                                        <label class="label py-1"><span class="label-text">Civitai API Key</span><span class="label-text-alt"> <a href="https://civitai.com/user/account" target="_blank" class="link link-primary text-xs">获取API Key</a> </span>
                                        </label>
                                        <div class="join">
                                            <input type="password" id="apiKeyInput" class="input input-bordered input-sm join-item flex-1" placeholder="请输入您的 Civitai API Key">
                                            <button type="button" id="saveApiKeyBtn" class="btn btn-primary btn-sm join-item">保&nbsp; 存
</button>
                                        </div>
                                        <label class="label py-1"><span class="label-text-alt text-info text-xs"> 
                                        API Key 用于访问 Civitai 的图片和模型数据 </span>
                                        </label>
                                    </div>
                                </form>
                            </div>
                    </div>
                    <!-- 主题配置卡片 -->
                    <div class="card bg-base-100 shadow-lg border border-base-300 h-auto max-w-3xl ml-auto mr-20 mx-8 mb-12">
                        <div class="card-body p-8">
                                <h2 class="card-title text-lg"> 
                            主题设置 </h2>
                                <p class="text-base-content/70 text-sm mb-3">选择您喜欢的界面主题和外观</p>
                                <div class="form-control">
                                    <label class="label py-1"><span class="label-text">选择主题</span><span class="label-text-alt text-xs">共32个主题可选</span>
                                    </label>
                                    <div class="dropdown dropdown-end">
                                        <div tabindex="0" role="button" class="btn btn-outline btn-sm justify-between w-full"><span class="flex items-center gap-2"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                                </svg>
                                        Theme </span>
                                            <svg width="12px" height="12px" class="inline-block h-2 w-2 fill-current opacity-60" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2048 2048">
                                                <path d="M1799 349l242 241-1017 1017L7 590l242-241 775 775 775-775z"></path>
                                            </svg>
                                        </div>
                                        <ul tabindex="0" class="dropdown-content bg-base-300 rounded-box z-1 w-52 p-2 shadow-2xl max-h-60 overflow-y-auto">
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌞 Light" value="light">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌙 Dark" value="dark">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🧁 Cupcake" value="cupcake">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🐝 Bumblebee" value="bumblebee">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💎 Emerald" value="emerald">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🏢 Corporate" value="corporate">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌆 Synthwave" value="synthwave">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="📼 Retro" value="retro">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🤖 Cyberpunk" value="cyberpunk">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💝 Valentine" value="valentine">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🎃 Halloween" value="halloween">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌸 Garden" value="garden">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌲 Forest" value="forest">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌊 Aqua" value="aqua">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🎵 Lofi" value="lofi">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🎨 Pastel" value="pastel">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🦄 Fantasy" value="fantasy">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="📐 Wireframe" value="wireframe">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="⚫ Black" value="black">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💰 Luxury" value="luxury">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🧛 Dracula" value="dracula">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🖨️ CMYK" value="cmyk">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🍂 Autumn" value="autumn">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💼 Business" value="business">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🧪 Acid" value="acid">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🍋 Lemonade" value="lemonade">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌃 Night" value="night">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="☕ Coffee" value="coffee">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="❄️ Winter" value="winter">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🔅 Dim" value="dim">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🏔️ Nord" value="nord">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌅 Sunset" value="sunset">
                                            </li>
                                        </ul>
                                    </div>
                                    <label class="label"><span class="label-text-alt text-info">主题设置会自动保存，侧边栏的切换开关可快速切换明暗主题 </span>
                                    </label>
                                </div>
                            </div>
                    </div>
                <!-- 图片展示面板 -->
                <div id="imagesPanel" class="tab-pane">
                    <div style="width: 100%; padding: 2rem;">
                        <div style="margin-bottom: 2rem;">
                            <h1 style="font-size: 2rem; font-weight: bold; color: #3b82f6; margin-bottom: 0.5rem;">图片展示</h1>
                            <p style="color: #6b7280;">从 Civitai 搜索和下载高质量的 AI 生成图片</p>
                        </div>

                        <!-- 搜索控制区 -->
                        <div class="card bg-base-100 shadow-lg mb-6">
                            <div class="card-body">
                                <h2 class="card-title mb-4">搜索与下载</h2>
                                <form id="imageSearchForm" class="space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text">用户名</span>
                                            </label>
                                            <input type="text" id="imageUsername" class="input input-bordered" placeholder="输入用户名">
                                        </div>
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text">模型ID</span>
                                            </label>
                                            <input type="text" id="imageModelId" class="input input-bordered" placeholder="输入模型ID">
                                        </div>
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text">下载数量</span>
                                            </label>
                                            <select id="imageMaxCount" class="select select-bordered">
                                                <option value="20">20张图片</option>
                                                <option value="50">50张图片</option>
                                                <option value="100">100张图片</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="flex gap-2">
                                        <button type="button" id="imageSearchBtn" class="btn btn-primary">搜索图片</button>
                                        <button type="button" id="imageDownloadBtn" class="btn btn-secondary">批量下载</button>
                                        <button type="button" id="imageDirectoryBtn" class="btn btn-outline">打开目录</button>
                                    </div>
                                    <div id="imageProgress" class="hidden">
                                        <progress class="progress progress-primary w-full" value="0" max="100"></progress>
                                        <div id="imageProgressText" class="text-sm text-center mt-2"></div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 图片展示区 -->
                        <div class="card bg-base-100 shadow-lg">
                            <div class="card-body">
                                <h2 class="card-title mb-4">图片画廊</h2>
                                <div id="imageGallery" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="text-center text-base-content/50 col-span-full py-12">
                                        <p>点击"搜索图片"开始浏览</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 元数据管理面板 -->
                <div id="metaPanel" class="tab-pane">
                    <div style="width: 100%; padding: 2rem;">
                        <div style="margin-bottom: 2rem;">
                            <h1 style="font-size: 2rem; font-weight: bold; color: #8b5cf6; margin-bottom: 0.5rem;">元数据管理</h1>
                            <p style="color: #6b7280;">管理和分析本地下载的图片元数据</p>
                        </div>

                        <!-- 统计信息 -->
                        <div class="stats stats-horizontal shadow-lg bg-base-100 w-full mb-6">
                            <div class="stat">
                                <div class="stat-title">总图片数</div>
                                <div class="stat-value text-primary" id="metaTotalImages">0</div>
                                <div class="stat-desc">本地下载的图片</div>
                            </div>
                            <div class="stat">
                                <div class="stat-title">分类数量</div>
                                <div class="stat-value text-secondary" id="metaTotalCategories">0</div>
                                <div class="stat-desc">不同的下载分类</div>
                            </div>
                            <div class="stat">
                                <div class="stat-title">用户数量</div>
                                <div class="stat-value text-accent" id="metaTotalUsers">0</div>
                                <div class="stat-desc">不同的创作者</div>
                            </div>
                            <div class="stat">
                                <div class="stat-title">模型数量</div>
                                <div class="stat-value text-info" id="metaTotalModels">0</div>
                                <div class="stat-desc">使用的AI模型</div>
                            </div>
                        </div>

                        <!-- 搜索筛选区 -->
                        <div class="card bg-base-100 shadow-lg mb-6">
                            <div class="card-body">
                                <h2 class="card-title mb-4">搜索与筛选</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">关键词搜索</span>
                                        </label>
                                        <input type="text" id="metaSearchInput" class="input input-bordered" placeholder="在提示词中搜索...">
                                    </div>
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">用户筛选</span>
                                        </label>
                                        <input type="text" id="metaUserInput" class="input input-bordered" placeholder="输入用户名...">
                                    </div>
                                </div>
                                <div class="flex gap-2">
                                    <button type="button" id="metaSearchBtn" class="btn btn-primary">搜索</button>
                                    <button type="button" id="metaClearBtn" class="btn btn-ghost">清除</button>
                                    <button type="button" id="metaRefreshBtn" class="btn btn-secondary">刷新统计</button>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索结果 -->
                        <div class="card bg-base-100 shadow-lg">
                            <div class="card-body">
                                <div class="flex justify-between items-center mb-4">
                                    <h2 class="card-title">搜索结果</h2>
                                    <div class="badge badge-info" id="metaResultInfo">点击"搜索"开始浏览本地元数据</div>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="table table-zebra">
                                        <thead>
                                            <tr>
                                                <th>预览</th>
                                                <th>ID</th>
                                                <th>用户</th>
                                                <th>模型</th>
                                                <th>尺寸</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="metaResultTable">
                                            <tr>
                                                <td colspan="6" class="text-center text-base-content/50 py-12">
                                                    <p>请先进行搜索以查看本地元数据</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="flex justify-center mt-4">
                                    <div class="join" id="metaPaginationControls" style="display: none;">
                                        <button class="join-item btn btn-outline" id="metaPrevBtn">上一页</button>
                                        <button class="join-item btn btn-active" id="metaPageInfo">1 / 1</button>
                                        <button class="join-item btn btn-outline" id="metaNextBtn">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <script src="main.js"></script>
    </body>
</html>
