<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>Civitai Pyweb Hello</title>
        <link href="tailwind.css" rel="stylesheet">
        <style>/* 动态卡片样式优化 */.card.h-auto { min-height: fit-content; transition: all 0.3s ease; } .card.h-auto:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); } /* 图片网格自适应 */.grid-cols-4 > .card.h-auto { display: flex; flex-direction: column; height: auto; } .grid-cols-4 > .card.h-auto .card-body { flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; } /* 响应式图片容器 */.card figure.h-auto { aspect-ratio: 1; overflow: hidden; } .card figure.h-auto img { width: 100%; height: 100%; object-fit: cover; } /* 文本内容自适应 */.card .min-h-8 { line-height: 1.2; word-wrap: break-word; } /* 统计卡片动态高度 */.stats.stats-horizontal { height: auto; min-height: fit-content; } .stats .stat { padding: 1rem; min-height: fit-content; }</style>
    </head>
    <body class="bg-base-200">
        <div class="flex h-screen">
            <!-- 侧边栏 -->
            <aside class="w-44 bg-base-100 shadow flex flex-col items-center py-6">
                <div class="text-2xl font-bold mb-8">Civitai Pyweb</div>
                <button class="btn btn-primary btn-block mb-2" id="btnSettings">
                    程序设置
</button>
                <button class="btn btn-ghost btn-block mb-2" id="btnImages">
                    图片展示
</button>
                <button class="btn btn-ghost btn-block mb-4" id="btnMeta">
                    元数据管理
</button>
                <div class="flex-grow"></div>
                <!-- 明暗主题切换开关 -->
                <div class="w-full px-2 mb-4">
                    <label class="flex cursor-pointer gap-2 items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sun">
                            <circle cx="12" cy="12" r="4"/>
                            <path d="M12 2v2"/>
                            <path d="M12 20v2"/>
                            <path d="m4.93 4.93 1.41 1.41"/>
                            <path d="m17.66 17.66 1.41 1.41"/>
                            <path d="M2 12h2"/>
                            <path d="M20 12h2"/>
                            <path d="m6.34 17.66-1.41 1.41"/>
                            <path d="m19.07 4.93-1.41 1.41"/>
                        </svg>
                        <input type="checkbox" id="themeToggle" class="toggle toggle-sm" onchange="toggleDarkMode(this)"/>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon">
                            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
                        </svg>
                    </label>
                </div>
            </aside>
            <!-- 主内容区 -->
            <section class="flex-1 p-12 overflow-y-auto">
                <!-- 程序设置面板 -->
                <div id="settingsPanel" class="tab-pane">
                    <!-- 页面标题区域 -->
                    <div class="bg-gradient-to-r from-primary/10 to-secondary/10 mb-16">
                        <div class="max-w-3xl mx-auto py-16 px-8 text-center">
                            <h1 class="text-4xl font-bold text-primary mb-4">程序设置</h1>
                            <p class="text-lg text-base-content/70">配置应用程序的基本设置和个性化选项</p>
                        </div>
                    </div>
                    <div class="max-w-3xl mx-auto px-8 space-y-12">
                        <!-- API 配置卡片 -->
                        <div class="card bg-base-100 shadow-lg border border-base-300 h-auto">
                            <div class="card-body p-8">
                                <h2 class="card-title text-lg"> 
                            API 配置 </h2>
                                <p class="text-base-content/70 text-sm mb-3">配置 Civitai API 密钥以访问在线服务</p>
                                <form id="settingsForm">
                                    <div class="form-control">
                                        <label class="label py-1"><span class="label-text">Civitai API Key</span><span class="label-text-alt"> <a href="https://civitai.com/user/account" target="_blank" class="link link-primary text-xs">获取API Key</a> </span>
                                        </label>
                                        <div class="join">
                                            <input type="password" id="apiKeyInput" class="input input-bordered input-sm join-item flex-1" placeholder="请输入您的 Civitai API Key">
                                            <button type="button" id="saveApiKeyBtn" class="btn btn-primary btn-sm join-item">保&nbsp; 存
</button>
                                        </div>
                                        <label class="label py-1"><span class="label-text-alt text-info text-xs"> 
                                        API Key 用于访问 Civitai 的图片和模型数据 </span>
                                        </label>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- 主题配置卡片 -->
                        <div class="card bg-base-100 shadow-lg border border-base-300 h-auto">
                            <div class="card-body p-8">
                                <h2 class="card-title text-lg"> 
                            主题设置 </h2>
                                <p class="text-base-content/70 text-sm mb-3">选择您喜欢的界面主题和外观</p>
                                <div class="form-control">
                                    <label class="label py-1"><span class="label-text">选择主题</span><span class="label-text-alt text-xs">共32个主题可选</span>
                                    </label>
                                    <div class="dropdown dropdown-end">
                                        <div tabindex="0" role="button" class="btn btn-outline btn-sm justify-between w-full"><span class="flex items-center gap-2"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                                </svg>
                                        Theme </span>
                                            <svg width="12px" height="12px" class="inline-block h-2 w-2 fill-current opacity-60" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2048 2048">
                                                <path d="M1799 349l242 241-1017 1017L7 590l242-241 775 775 775-775z"></path>
                                            </svg>
                                        </div>
                                        <ul tabindex="0" class="dropdown-content bg-base-300 rounded-box z-1 w-52 p-2 shadow-2xl max-h-60 overflow-y-auto">
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌞 Light" value="light">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌙 Dark" value="dark">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🧁 Cupcake" value="cupcake">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🐝 Bumblebee" value="bumblebee">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💎 Emerald" value="emerald">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🏢 Corporate" value="corporate">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌆 Synthwave" value="synthwave">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="📼 Retro" value="retro">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🤖 Cyberpunk" value="cyberpunk">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💝 Valentine" value="valentine">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🎃 Halloween" value="halloween">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌸 Garden" value="garden">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌲 Forest" value="forest">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌊 Aqua" value="aqua">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🎵 Lofi" value="lofi">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🎨 Pastel" value="pastel">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🦄 Fantasy" value="fantasy">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="📐 Wireframe" value="wireframe">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="⚫ Black" value="black">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💰 Luxury" value="luxury">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🧛 Dracula" value="dracula">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🖨️ CMYK" value="cmyk">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🍂 Autumn" value="autumn">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="💼 Business" value="business">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🧪 Acid" value="acid">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🍋 Lemonade" value="lemonade">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌃 Night" value="night">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="☕ Coffee" value="coffee">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="❄️ Winter" value="winter">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🔅 Dim" value="dim">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🏔️ Nord" value="nord">
                                            </li>
                                            <li>
                                                <input type="radio" name="theme-dropdown" class="theme-controller w-full btn btn-sm btn-block btn-ghost justify-start" aria-label="🌅 Sunset" value="sunset">
                                            </li>
                                        </ul>
                                    </div>
                                    <label class="label"><span class="label-text-alt text-info">主题设置会自动保存，侧边栏的切换开关可快速切换明暗主题 </span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 图片展示面板 -->
                <div id="imagesPanel" class="tab-pane">
                    <!-- 页面标题区域 -->
                    <div class="bg-gradient-to-r from-secondary/10 to-accent/10 mb-16">
                        <div class="max-w-7xl mx-auto py-16 px-8 text-center">
                            <h1 class="text-4xl font-bold text-secondary mb-4">图片展示</h1>
                            <p class="text-lg text-base-content/70">从 Civitai 搜索和下载高质量的 AI 生成图片</p>
                        </div>
                    </div>
                    <div class="max-w-7xl mx-auto px-8 space-y-12">
                        <!-- 搜索和下载控制卡片 -->
                        <div class="card bg-base-100 shadow-lg border border-base-300 h-auto">
                            <div class="card-body p-8">
                                <h2 class="card-title text-lg"> 
                            搜索与下载 </h2>
                                <p class="text-base-content/70 text-sm mb-4">通过用户名、模型ID或模型版本ID来搜索和批量下载图片</p>
                                <form id="batchDownloadForm" class="space-y-4">
                                    <!-- 搜索条件 -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="form-control">
                                            <label class="label py-1">
                                                <span class="label-text text-sm">用户名 </span>
                                            </label>
                                            <input type="text" id="username" name="username" class="input input-bordered input-sm" placeholder="例如: john_doe">
                                            <label class="label py-0">
                                                <span class="label-text-alt text-xs">按创作者用户名搜索</span>
                                            </label>
                                        </div>
                                        <div class="form-control">
                                            <label class="label py-1">
                                                <span class="label-text text-sm">模型ID </span>
                                            </label>
                                            <input type="text" id="modelId" name="modelId" class="input input-bordered input-sm" placeholder="例如: 123456">
                                            <label class="label py-0">
                                                <span class="label-text-alt text-xs">按AI模型ID搜索</span>
                                            </label>
                                        </div>
                                        <div class="form-control">
                                            <input type="text" id="modelVersionId" name="modelVersionId" class="input input-bordered input-sm" placeholder="例如: 789012">
                                            <label class="label py-1">
                                                <span class="label-text text-sm">模型版本ID </span>
                                            </label>
                                            <label class="label py-0">
                                                <span class="label-text-alt text-xs">按模型版本ID搜索</span>
                                            </label>
                                        </div>
                                    </div>
                                    <!-- 第二行：下载数量和操作按钮 -->
                                    <div class="grid grid-cols-2 gap-4 items-end">
                                        <div class="form-control">
                                            <label class="label py-1"><span class="label-text text-sm"> 
                                            下载数量 </span>
                                            </label>
                                            <select id="maxCountSelect" name="maxCountSelect" class="select select-bordered select-sm">
                                                <option value="20" selected="">20张图片</option>
                                                <option value="100">100张图片</option>
                                                <option value="200">200张图片</option>
                                                <option value="-1">下载全部</option>
                                            </select>
                                            <label class="label py-0"><span class="label-text-alt text-xs">选择下载数量限制</span>
                                            </label>
                                        </div>
                                        <!-- 操作按钮 -->
                                        <div class="flex flex-wrap gap-2">
                                            <button type="submit" id="downloadBtn" class="btn btn-primary btn-sm">
                                                开始下载
</button>
                                            <button type="button" id="cancelBtn" class="btn btn-warning btn-sm" style="display:none;">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                                取消
                                            </button>
                                            <button type="button" id="searchBtn" class="btn btn-secondary btn-sm">
                                                搜索
</button>
                                            <button type="button" id="directoryBtn" class="btn btn-outline btn-sm" onclick="showDirectoryStructure()">
                                                目录
</button>
                                        </div>
                                    </div>
                                    <!-- 进度显示 -->
                                    <div class="space-y-2">
                                        <progress id="progressBar" class="progress progress-primary w-full" value="0" max="100" style="display:none;"></progress>
                                        <div class="flex justify-between items-center">
                                            <div id="progressText" class="text-xs text-base-content/70"></div>
                                            <div id="statusText" class="text-xs text-error"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- 图片展示区域 -->
                        <div class="card bg-base-100 shadow-lg border border-base-300 h-auto">
                            <div class="card-body mr-8 p-8">
                                <h2 class="card-title text-lg"> 
                            图片画廊 </h2>
                                <p class="text-base-content/70 text-sm mb-3">搜索结果将在这里显示，点击搜索按钮开始浏览</p>
                                <div id="imageList" class="grid grid-cols-4 gap-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 元数据管理面板 -->
                <div id="metaPanel" class="tab-pane">
                    <!-- 页面标题区域 -->
                    <div class="bg-gradient-to-r from-accent/10 to-info/10 mb-16">
                        <div class="max-w-7xl mx-auto py-16 px-8 text-center">
                            <h1 class="font-bold mb-4 text-8xl text-accent">元数据管理</h1>
                            <p class="text-lg text-base-content/70">管理和分析本地下载的图片元数据</p>
                        </div>
                    </div>
                    <div class="max-w-7xl mx-auto px-8 space-y-12">
                        <!-- 统计信息卡片 -->
                        <div class="stats stats-horizontal shadow-lg bg-base-100 w-full border border-base-300">
                            <div class="stat">
                                <div class="stat-figure text-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="stat-title text-sm">总图片数</div>
                                <div class="stat-value text-primary text-2xl" id="totalImages">0</div>
                                <div class="stat-desc text-xs">本地下载的图片</div>
                            </div>
                            <div class="stat">
                                <div class="stat-figure text-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="stat-title text-sm">分类数量</div>
                                <div class="stat-value text-secondary text-2xl" id="totalCategories">0</div>
                                <div class="stat-desc text-xs">不同的下载分类</div>
                            </div>
                            <div class="stat">
                                <div class="stat-figure text-accent">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="stat-title text-sm">用户数量</div>
                                <div class="stat-value text-accent text-2xl" id="totalUsers">0</div>
                                <div class="stat-desc text-xs">不同的创作者</div>
                            </div>
                            <div class="stat">
                                <div class="stat-figure text-info">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <div class="stat-title text-sm">模型数量</div>
                                <div class="stat-value text-info text-2xl" id="totalModels">0</div>
                                <div class="stat-desc text-xs">使用的AI模型</div>
                            </div>
                        </div>
                        <!-- 搜索和筛选 -->
                        <div class="card bg-base-100 shadow-lg border border-base-300 h-auto">
                            <div class="card-body p-8">
                                <h2 class="card-title text-lg"> 
                            搜索与筛选 </h2>
                                <p class="text-base-content/70 text-sm mb-4">在本地元数据中搜索和筛选图片</p>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="form-control">
                                        <label class="label py-1"><span class="label-text text-sm"> 
                                        关键词搜索 </span>
                                        </label>
                                        <input type="text" id="metaSearchQuery" class="input input-bordered input-sm" placeholder="在提示词中搜索...">
                                        <label class="label py-0"><span class="label-text-alt text-xs">搜索提示词和负面提示词</span>
                                        </label>
                                    </div>
                                    <div class="form-control">
                                        <label class="label py-1"><span class="label-text text-sm"> 
                                        分类筛选 </span>
                                        </label>
                                        <select id="metaCategoryFilter" class="select select-bordered select-sm">
                                            <option value="all">所有分类</option>
                                        </select>
                                        <label class="label py-0"><span class="label-text-alt text-xs">按下载分类筛选</span>
                                        </label>
                                    </div>
                                    <div class="form-control">
                                        <label class="label py-1"><span class="label-text text-sm"> 
                                        用户筛选 </span>
                                        </label>
                                        <input type="text" id="metaUserFilter" class="input input-bordered input-sm" placeholder="用户名...">
                                        <label class="label py-0"><span class="label-text-alt text-xs">按创作者用户名筛选</span>
                                        </label>
                                    </div>
                                    <div class="form-control">
                                        <label class="label py-1"><span class="label-text text-sm"> 
                                        内容筛选 </span>
                                        </label>
                                        <select id="metaNsfwFilter" class="select select-bordered select-sm">
                                            <option value="all">全部内容</option>
                                            <option value="sfw">安全内容</option>
                                            <option value="nsfw">成人内容</option>
                                        </select>
                                        <label class="label py-0"><span class="label-text-alt text-xs">按内容类型筛选</span>
                                        </label>
                                    </div>
                                </div>
                                <!-- 第二行：操作按钮 -->
                                <div class="grid grid-cols-2 gap-4 items-end">
                                    <div></div>                                     
                                    <!-- 空白占位 -->
                                    <div class="flex flex-wrap gap-2 justify-end">
                                        <button class="btn btn-primary btn-sm" onclick="searchMetadata()">
                                            搜索
</button>
                                        <button class="btn btn-ghost btn-sm" onclick="clearMetaFilters()">
                                            清除
</button>
                                        <button class="btn btn-secondary btn-sm" onclick="refreshMetadata()">
                                            刷新
</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 搜索结果 -->
                        <div class="card bg-base-100 shadow-lg border border-base-300 h-auto">
                            <div class="card-body p-8">
                                <div class="flex justify-between items-center gap-4">
                                    <h2 class="card-title text-lg"> 
                                搜索结果 </h2>
                                    <div class="badge badge-info" id="metaSearchInfo">
                                        点击&quot;搜索&quot;开始浏览本地元数据
</div>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="table table-zebra table-pin-rows">
                                        <thead>
                                            <tr class="bg-base-200">
                                                <th class="text-center text-sm"> 
                                            预览 </th>
                                                <th class="text-sm"> 
                                            ID </th>
                                                <th class="text-sm"> 
                                            用户 </th>
                                                <th class="text-sm"> 
                                            模型 </th>
                                                <th class="text-sm"> 
                                            尺寸 </th>
                                                <th class="text-sm"> 
                                            分类 </th>
                                                <th class="text-center text-sm"> 
                                            操作 </th>
                                            </tr>
                                        </thead>
                                        <tbody id="metaSearchResults">
                                            <tr>
                                                <td colspan="7" class="text-center text-base-content/50 py-12">
                                                    <div class="flex flex-col items-center gap-3">
                                                        <div class="text-lg font-medium">暂无数据</div>
                                                        <div class="text-sm">请先进行搜索以查看本地元数据</div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!-- 分页控件 -->
                                <div class="flex justify-center mt-6">
                                    <div class="join" id="metaPagination" style="display: none;">
                                        <button class="join-item btn btn-outline" onclick="prevMetaPage()">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="15 19l-7-7 7-7"></path>
                                            </svg>
                                            上一页
                                        </button>
                                        <button class="join-item btn btn-active" id="currentMetaPage">1 / 1</button>
                                        <button class="join-item btn btn-outline" onclick="nextMetaPage()">
                                            下一页
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <script src="main.js"></script>
    </body>
</html>
