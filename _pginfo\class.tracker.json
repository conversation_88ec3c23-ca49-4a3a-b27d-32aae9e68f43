{"list": " bg-base-200 flex h-screen w-44 bg-base-100 shadow flex-col items-center py-6 text-2xl font-bold mb-8 btn btn-primary btn-block mb-2 btn-ghost mb-4 flex-grow w-full px-2 cursor-pointer gap-2 justify-center lucide lucide-sun toggle toggle-sm lucide-moon flex-1 p-12 overflow-y-auto tab-pane bg-gradient-to-r from-primary/10 to-secondary/10 mb-16 max-w-3xl mx-auto py-16 px-8 text-center text-4xl text-primary text-lg text-base-content/70 space-y-12 card shadow-lg border border-base-300 card-body p-8 card-title h-5 w-5 text-sm mb-3 form-control label py-1 label-text label-text-alt link link-primary text-xs join input input-bordered input-sm join-item btn-sm h-4 w-4 mr-1 text-info h-3 w-3 inline dropdown dropdown-end btn-outline justify-between inline-block h-2 w-2 fill-current opacity-60 dropdown-content bg-base-300 rounded-box z-1 w-52 p-2 shadow-2xl max-h-60 theme-controller justify-start from-secondary/10 to-accent/10 max-w-7xl text-secondary space-y-4 grid grid-cols-2 gap-4 py-0 items-end select select-bordered select-sm flex-wrap btn-warning btn-secondary space-y-2 progress progress-primary text-error gap-6 from-accent/10 to-info/10 text-accent stats stats-horizontal stat stat-figure h-6 w-6 stat-title stat-value stat-desc justify-end badge badge-info divider my-2 overflow-x-auto table table-zebra table-pin-rows text-base-content/50 py-12 gap-3 h-12 w-12 text-base-content/30 font-medium mt-6 btn-active h-auto grid-cols-4 mr-8", "map": {"bg-base-200": true, "flex": true, "h-screen": true, "w-44": true, "bg-base-100": true, "shadow": true, "flex-col": true, "items-center": true, "py-6": true, "text-2xl": true, "font-bold": true, "mb-8": true, "btn": true, "btn-primary": true, "btn-block": true, "mb-2": true, "btn-ghost": true, "mb-4": true, "flex-grow": true, "w-full": true, "px-2": true, "cursor-pointer": true, "gap-2": true, "justify-center": true, "lucide": true, "lucide-sun": true, "toggle": true, "toggle-sm": true, "lucide-moon": true, "flex-1": true, "p-12": true, "overflow-y-auto": true, "tab-pane": true, "bg-gradient-to-r": true, "from-primary/10": true, "to-secondary/10": true, "mb-16": true, "max-w-3xl": true, "mx-auto": true, "py-16": true, "px-8": true, "text-center": true, "text-4xl": true, "text-primary": true, "text-lg": true, "text-base-content/70": true, "space-y-12": true, "card": true, "shadow-lg": true, "border": true, "border-base-300": true, "card-body": true, "p-8": true, "card-title": true, "h-5": true, "w-5": true, "text-sm": true, "mb-3": true, "form-control": true, "label": true, "py-1": true, "label-text": true, "label-text-alt": true, "link": true, "link-primary": true, "text-xs": true, "join": true, "input": true, "input-bordered": true, "input-sm": true, "join-item": true, "btn-sm": true, "h-4": true, "w-4": true, "mr-1": true, "text-info": true, "h-3": true, "w-3": true, "inline": true, "dropdown": true, "dropdown-end": true, "btn-outline": true, "justify-between": true, "inline-block": true, "h-2": true, "w-2": true, "fill-current": true, "opacity-60": true, "dropdown-content": true, "bg-base-300": true, "rounded-box": true, "z-1": true, "w-52": true, "p-2": true, "shadow-2xl": true, "max-h-60": true, "theme-controller": true, "justify-start": true, "from-secondary/10": true, "to-accent/10": true, "max-w-7xl": true, "text-secondary": true, "space-y-4": true, "grid": true, "grid-cols-2": true, "gap-4": true, "py-0": true, "items-end": true, "select": true, "select-bordered": true, "select-sm": true, "flex-wrap": true, "btn-warning": true, "btn-secondary": true, "space-y-2": true, "progress": true, "progress-primary": true, "text-error": true, "gap-6": true, "from-accent/10": true, "to-info/10": true, "text-accent": true, "stats": true, "stats-horizontal": true, "stat": true, "stat-figure": true, "h-6": true, "w-6": true, "stat-title": true, "stat-value": true, "stat-desc": true, "justify-end": true, "badge": true, "badge-info": true, "divider": true, "my-2": true, "overflow-x-auto": true, "table": true, "table-zebra": true, "table-pin-rows": true, "text-base-content/50": true, "py-12": true, "gap-3": true, "h-12": true, "w-12": true, "text-base-content/30": true, "font-medium": true, "mt-6": true, "btn-active": true, "h-auto": true, "grid-cols-4": true, "mr-8": true}}