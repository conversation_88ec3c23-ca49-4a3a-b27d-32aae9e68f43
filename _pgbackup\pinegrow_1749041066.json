{"files": {"frontend\\index.html": {"frameworks": ["Civitai_Pyweb", "pg.svg.lib", "pg.tw.lib", "pg.insight.events", "pg.asset.manager", "pg.project.items", "pg.code-validator", "pg.css.grid", "pg.image.overlay", "tw.flowbite", "tw", "pg.html", "pine.cone.lib", "pg.components"]}}, "open-pages": ["index.html", "frontend/index.html"], "urls": {"frontend/index.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}}, "active-design-provider": "tw", "breakpoints": ["48rem", "640px", "768px", "1024px", "1280px", "1536px"], "frameworks": ["Civitai_Pyweb", "pg.svg.lib", "pg.tw.lib", "pg.insight.events", "pg.asset.manager", "pg.project.items", "pg.code-validator", "pg.css.grid", "pg.image.overlay", "tw.flowbite", "tw", "pg.html", "pine.cone.lib", "pg.components"], "recent-classes": ["row-end-3", "row-start-2", "col-end-2", "col-start-1"]}