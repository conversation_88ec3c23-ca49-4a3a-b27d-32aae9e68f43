@import "tailwindcss";

/* 面板切换样式 */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block !important;
    width: 100% !important;
    height: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
}

/* 确保面板内容可见 */
.tab-pane.active > * {
    display: block !important;
    visibility: visible !important;
}

.tab-pane.active .card {
    display: block !important;
    visibility: visible !important;
}

.tab-pane.active .card-body {
    display: block !important;
    visibility: visible !important;
}

/* 自定义样式 */
.sidebar {
    transition: transform 0.3s ease;
}

.content {
    transition: margin-left 0.3s ease;
}