{"files": {"frontend\\index.html": {"frameworks": ["Civitai_Pyweb", "pg.svg.lib", "pg.insight.events", "pg.tw.lib", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "tw.flowbite", "tw", "pg.html", "pine.cone.lib", "pg.components"]}}, "open-pages": ["index.html", "frontend/index.html"], "urls": {"frontend/index.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}}, "active-design-provider": "tw", "breakpoints": ["48rem", "640px", "768px", "1024px", "1280px", "1536px"], "frameworks": ["Civitai_Pyweb", "pg.svg.lib", "pg.insight.events", "pg.tw.lib", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "tw.flowbite", "tw", "pg.html", "pine.cone.lib", "pg.components"]}