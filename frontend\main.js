document.addEventListener('DOMContentLoaded', function() {
    console.log('前端JS已加载，Hello from main.js!');

    // 初始化主题
    initializeTheme();

    const form = document.getElementById('batchDownloadForm');
    // const progressBarContainer = document.getElementById('progressBarContainer'); // Removed
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    const apiKeyInput = document.getElementById('apiKeyInput');
    const saveApiKeyBtn = document.getElementById('saveApiKeyBtn');

    window.updateDownloadProgress = function(current, total) {
        if (progressBar) progressBar.style.display = 'block';
        let percent = total ? Math.round((current / total) * 100) : 0;
        if (progressBar) progressBar.value = percent;
        if (progressText) progressText.textContent = `已下载：${current}${total ? ' / ' + total : ''}`;
    };
    window.downloadComplete = function() {
        if (progressText) progressText.textContent += '  下载完成！';
        setTimeout(() => {
            if (progressBar) progressBar.style.display = 'none';
            if (progressBar) progressBar.value = 0;
        }, 3000);
    };

    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            if (progressBar) progressBar.style.display = 'block';
            if (progressBar) progressBar.value = 0;
            if (progressText) progressText.textContent = '开始下载...';
            const username = document.getElementById('username').value || undefined;
            const modelId = document.getElementById('modelId').value || undefined;
            const modelVersionId = document.getElementById('modelVersionId').value || undefined;
            const maxCountSelect = document.getElementById('maxCountSelect');
            const maxCountValue = maxCountSelect ? parseInt(maxCountSelect.value) : 20; // Default to 20 if not found
            
            // 默认保存到downloads目录
            const saveDir = 'downloads';
            try {
                if (window.pywebview && window.pywebview.api) {
                    await window.pywebview.api.batch_download(
                        saveDir,
                        username,
                        modelId ? parseInt(modelId) : undefined,
                        modelVersionId ? parseInt(modelVersionId) : undefined,
                        20, // This seems to be a 'limit' parameter for API pagination, not total download. Keep as is or clarify.
                        maxCountValue === -1 ? undefined : maxCountValue // Pass undefined for "all", otherwise the selected number
                    );
                } else {
                    alert('pywebview API 不可用');
                }
            } catch (err) {
                progressText.textContent = '下载出错：' + err;
            }
        });
    }

    // 使用HTML中已有的搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    // form.appendChild(searchBtn); // Removed, button is already in HTML

    if (searchBtn) {
        searchBtn.addEventListener('click', async function() {
            const imageList = document.getElementById('imageList');
            const progressText = document.getElementById('progressText');

            if (imageList) imageList.innerHTML = '';
            if (progressText) progressText.textContent = '正在搜索...';
        const username = document.getElementById('username').value || undefined;
        const modelId = document.getElementById('modelId').value || undefined;
        const modelVersionId = document.getElementById('modelVersionId').value || undefined;
        const maxCountSelect = document.getElementById('maxCountSelect');
        // For get_images, the 'limit' parameter usually means how many items per page.
        // If "Download All" (-1) is selected for search, we might want to fetch a large number or handle pagination if the API supports it.
        // For now, let's use the selected value as the limit, or a default if "Download All" is chosen for search.
        // The backend get_images might need adjustment if -1 is passed for 'limit'.
        // Let's assume for search, "Download All" means a reasonably large number like 200, or the API's max if known.
        // Or, we can interpret "Download All" for search as "show the first N results" where N is a large number.
        // For simplicity, if -1, we'll pass undefined to get_images, which might mean default API limit.
        // Or, we can pass a large number like 200 if -1 is selected for search.
        // Let's use the selected value directly, and if it's -1, pass undefined (or a default like 20).
        // The 'limit' parameter in get_images is for pagination, 'page' is also relevant.
        // The 'max_count' for batch_download is the total.
        // For get_images, 'limit' is items per request.
        // We'll use the selected value as the 'limit' for get_images. If -1, use a default (e.g., 20 or 100).
        let searchLimit = maxCountSelect ? parseInt(maxCountSelect.value) : 20;
        if (searchLimit === -1) {
            searchLimit = 100; // Default to 100 if "Download All" is selected for search preview
        }

        try {
            if (window.pywebview && window.pywebview.api) {
                const res = await window.pywebview.api.get_images(
                    username,
                    modelId ? parseInt(modelId) : undefined,
                    modelVersionId ? parseInt(modelVersionId) : undefined,
                    searchLimit, // Use the value from the dropdown for the number of images to fetch for preview
                    undefined // page parameter, not used here
                );
                if (res && res.items && res.items.length > 0) {
                    renderImageList(res.items);
                    if (progressText) progressText.textContent = `共${res.items.length}张图片`;
                } else {
                    if (progressText) progressText.textContent = '未找到图片';
                }
            } else {
                alert('pywebview API 不可用');
            }
        } catch (err) {
            if (progressText) progressText.textContent = '搜索出错：' + err;
        }
    });
    } // Closing brace for if(searchBtn)

    function renderImageList(items) {
        const imageList = document.getElementById('imageList');
        if (!imageList) return;

        imageList.innerHTML = ''; // Clear previous images

        items.forEach(item => {
            const card = document.createElement('div');
            card.className = 'card bg-base-100 shadow-xl'; // DaisyUI card

            const figure = document.createElement('figure');
            figure.className = 'px-2 pt-2 h-32'; // Reduced height for more compact layout
            const img = document.createElement('img');
            img.src = item.url;
            img.alt = 'Image ID: ' + item.id;
            img.className = 'rounded-xl object-cover w-full h-full'; // DaisyUI image styling

            // 添加图片加载错误处理
            img.onerror = function() {
                console.log(`图片加载失败: ${item.id} - ${item.url}`);
                // 创建占位符
                const placeholder = document.createElement('div');
                placeholder.className = 'rounded-xl bg-base-300 w-full h-full flex items-center justify-center text-base-content/50';
                placeholder.innerHTML = `<div class="text-center"><div class="text-xs">图片加载失败</div><div class="text-xs">ID: ${item.id}</div></div>`;
                figure.replaceChild(placeholder, img);
            };

            img.onload = function() {
                console.log(`图片加载成功: ${item.id}`);
            };

            figure.appendChild(img);

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body items-center text-center p-2'; // Reduced padding for compact layout

            const idDiv = document.createElement('p'); // Use <p> for text
            idDiv.textContent = 'ID: ' + item.id;
            idDiv.className = 'text-xs text-base-content/70 mt-1'; // DaisyUI text styling

            const metaDiv = document.createElement('p'); // Use <p>
            const promptText = item.meta && item.meta.prompt ? item.meta.prompt : 'No prompt available';
            metaDiv.textContent = promptText.length > 30 ? promptText.slice(0, 27) + '...' : promptText;
            metaDiv.title = promptText; // Show full prompt on hover
            metaDiv.className = 'text-xs text-base-content/90 h-8 overflow-hidden'; // Reduced height for compact layout

            const cardActions = document.createElement('div');
            cardActions.className = 'card-actions justify-center mt-1'; // Reduced margin for compact layout

            const dlBtn = document.createElement('button');
            dlBtn.textContent = '下载';
            dlBtn.className = 'btn btn-primary btn-sm'; // DaisyUI button

            dlBtn.onclick = async function() {
                dlBtn.disabled = true;
                dlBtn.classList.add('loading'); // DaisyUI loading spinner
                dlBtn.textContent = '下载中...'; // Text content changes while loading
                
                try {
                    const res = await window.pywebview.api.download_single_image(item.id, 'downloads');
                    
                    dlBtn.classList.remove('loading');
                    // dlBtn.disabled = false; // Re-enable after state change

                    if (res && res.success) {
                        dlBtn.textContent = '已下载';
                        dlBtn.classList.remove('btn-primary', 'btn-error');
                        dlBtn.classList.add('btn-success');
                    } else {
                        dlBtn.textContent = '失败';
                        dlBtn.classList.remove('btn-primary', 'btn-success');
                        dlBtn.classList.add('btn-error');
                    }
                } catch (error) {
                    console.error("Download error:", error);
                    dlBtn.classList.remove('loading');
                    dlBtn.textContent = '错误';
                    dlBtn.classList.remove('btn-primary', 'btn-success');
                    dlBtn.classList.add('btn-error');
                }


                setTimeout(() => {
                    dlBtn.disabled = false;
                    dlBtn.textContent = '下载';
                    dlBtn.classList.remove('btn-success', 'btn-error', 'loading');
                    dlBtn.classList.add('btn-primary');
                }, 3000);
            };

            cardBody.appendChild(idDiv);
            cardBody.appendChild(metaDiv);
            cardActions.appendChild(dlBtn);
            cardBody.appendChild(cardActions);

            card.appendChild(figure);
            card.appendChild(cardBody);

            if (imageList) imageList.appendChild(card); // Append card to imageList directly
        });
    }

    if (saveApiKeyBtn) {
        saveApiKeyBtn.addEventListener('click', async function() {
            const key = apiKeyInput.value.trim();
            if (!key) {
                alert('请输入API Key');
                return;
            }
            try {
                if (window.pywebview && window.pywebview.api) {
                    await window.pywebview.api.set_api_key(key);
                    alert('API Key已保存！');
                } else {
                    alert('pywebview API 不可用');
                }
            } catch (err) {
                alert('保存API Key失败：' + err);
            }
        });
    }

    // 侧边栏切换逻辑
    const btnSettings = document.getElementById('btnSettings');
    const btnImages = document.getElementById('btnImages');
    const btnMeta = document.getElementById('btnMeta');
    const settingsPanel = document.getElementById('settingsPanel');
    const imagesPanel = document.getElementById('imagesPanel');
    const metaPanel = document.getElementById('metaPanel');

    // 默认显示图片面板，隐藏其他面板
    if(settingsPanel) settingsPanel.style.display = 'none';
    if(imagesPanel) imagesPanel.style.display = 'block'; // Default visible
    if(metaPanel) metaPanel.style.display = 'none';
    if(btnImages) btnImages.classList.add('btn-primary'); // Set initial active button
    if(btnSettings) btnSettings.classList.remove('btn-primary');
    if(btnSettings) btnSettings.classList.add('btn-ghost');
    if(btnMeta) btnMeta.classList.remove('btn-primary');
    if(btnMeta) btnMeta.classList.add('btn-ghost');


    function setActivePanel(panelId) {
        const panels = {
            settings: settingsPanel,
            images: imagesPanel,
            meta: metaPanel
        };
        const buttons = {
            settings: btnSettings,
            images: btnImages,
            meta: btnMeta
        };

        // Hide all panels and reset button styles
        for (const key in panels) {
            if (panels[key]) panels[key].style.display = 'none';
            if (buttons[key]) {
                buttons[key].classList.remove('btn-primary');
                buttons[key].classList.add('btn-ghost');
            }
        }

        // Show the selected panel and set button style
        if (panels[panelId]) panels[panelId].style.display = 'block';
        if (buttons[panelId]) {
            buttons[panelId].classList.remove('btn-ghost');
            buttons[panelId].classList.add('btn-primary');
        }
    }

    if(btnSettings) btnSettings.addEventListener('click', function() { setActivePanel('settings'); });
    if(btnImages) btnImages.addEventListener('click', function() { setActivePanel('images'); });
    if(btnMeta) btnMeta.addEventListener('click', function() {
        setActivePanel('meta');
        // 切换到元数据面板时自动刷新统计
        refreshMetadata();
    });

    // Ensure initial state is correct
    setActivePanel('images'); // Or your preferred default panel
});

// 打开下载目录
async function showDirectoryStructure() {
    try {
        const result = await pywebview.api.open_download_directory();
        console.log('打开目录结果:', result);

        if (result.success) {
            console.log('已打开下载目录:', result.path);
            // 可选：显示一个简短的提示
            const progressText = document.getElementById('progressText');
            if (progressText) {
                const originalText = progressText.textContent;
                progressText.textContent = '已打开下载目录';
                setTimeout(() => {
                    progressText.textContent = originalText;
                }, 2000);
            }
        } else {
            alert('打开目录失败: ' + result.error);
        }
    } catch (error) {
        console.error('打开目录失败:', error);
        alert('打开目录失败: ' + error.message);
    }
}

// 监听主题控制器变化
function initializeThemeController() {
    // 获取所有主题控制器
    const themeControllers = document.querySelectorAll('.theme-controller');

    // 为每个主题控制器添加事件监听
    themeControllers.forEach(controller => {
        controller.addEventListener('change', function() {
            if (this.checked) {
                const themeName = this.value;
                console.log('主题已切换到:', themeName);

                // 更新侧边栏的明暗切换开关状态
                updateSidebarToggle(themeName);

                // 保存主题到本地存储
                localStorage.setItem('selectedTheme', themeName);
            }
        });
    });

    // 恢复保存的主题
    const savedTheme = localStorage.getItem('selectedTheme') || 'light';
    setTheme(savedTheme);
}

// 设置主题
function setTheme(themeName) {
    // 设置data-theme属性
    document.documentElement.setAttribute('data-theme', themeName);

    // 选中对应的radio按钮
    const themeController = document.querySelector(`input[name="theme-dropdown"][value="${themeName}"]`);
    if (themeController) {
        themeController.checked = true;
    }

    // 更新侧边栏切换开关
    updateSidebarToggle(themeName);
}

// 更新侧边栏明暗切换开关状态
function updateSidebarToggle(themeName) {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const darkThemes = ['dark', 'night', 'black', 'dracula', 'halloween', 'forest', 'luxury', 'business', 'coffee', 'dim'];
        themeToggle.checked = darkThemes.includes(themeName);
    }
}

// 明暗主题切换函数（侧边栏开关）
function toggleDarkMode(toggle) {
    const isDark = toggle.checked;
    const newTheme = isDark ? 'dark' : 'light';
    setTheme(newTheme);

    // 保存主题到本地存储
    localStorage.setItem('selectedTheme', newTheme);
}

// 页面加载时初始化主题
function initializeTheme() {
    initializeThemeController();
}



// 元数据管理相关变量
let currentMetaData = [];
let currentMetaPage = 1;
const metaItemsPerPage = 20;

// 刷新元数据统计
async function refreshMetadata() {
    try {
        const stats = await window.pywebview.api.get_metadata_statistics();

        // 更新统计卡片
        document.getElementById('totalImages').textContent = stats.total_images || 0;
        document.getElementById('totalCategories').textContent = Object.keys(stats.categories || {}).length;
        document.getElementById('totalUsers').textContent = Object.keys(stats.users || {}).length;
        document.getElementById('totalModels').textContent = Object.keys(stats.models || {}).length;

        // 更新分类筛选下拉框
        const categoryFilter = document.getElementById('metaCategoryFilter');
        categoryFilter.innerHTML = '<option value="all">所有分类</option>';

        if (stats.top_categories) {
            stats.top_categories.forEach(([category, count]) => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = `${category} (${count})`;
                categoryFilter.appendChild(option);
            });
        }

        console.log('元数据统计已刷新:', stats);
    } catch (error) {
        console.error('刷新元数据统计失败:', error);
        alert('刷新统计信息失败: ' + error.message);
    }
}

// 搜索元数据
async function searchMetadata() {
    try {
        const query = document.getElementById('metaSearchQuery').value;
        const category = document.getElementById('metaCategoryFilter').value;
        const username = document.getElementById('metaUserFilter').value;
        const nsfwFilter = document.getElementById('metaNsfwFilter').value;

        const result = await window.pywebview.api.search_local_metadata(
            query, category, username, '', nsfwFilter
        );

        currentMetaData = result.metadata || [];
        currentMetaPage = 1;

        // 更新搜索信息
        const searchInfo = document.getElementById('metaSearchInfo');
        searchInfo.textContent = `找到 ${result.total_results} 条结果 (共扫描 ${result.total_scanned} 条记录)`;

        // 渲染搜索结果
        renderMetaSearchResults();

        console.log('元数据搜索完成:', result);
    } catch (error) {
        console.error('搜索元数据失败:', error);
        alert('搜索失败: ' + error.message);
    }
}

// 渲染元数据搜索结果
function renderMetaSearchResults() {
    const tbody = document.getElementById('metaSearchResults');
    const pagination = document.getElementById('metaPagination');

    if (!currentMetaData || currentMetaData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-base-content/50">
                    没有找到匹配的结果
                </td>
            </tr>
        `;
        pagination.style.display = 'none';
        return;
    }

    // 计算分页
    const startIndex = (currentMetaPage - 1) * metaItemsPerPage;
    const endIndex = Math.min(startIndex + metaItemsPerPage, currentMetaData.length);
    const pageData = currentMetaData.slice(startIndex, endIndex);

    // 渲染表格行
    tbody.innerHTML = '';
    pageData.forEach(item => {
        const row = document.createElement('tr');

        // 预览图
        const previewCell = document.createElement('td');
        if (item.image_exists && item.image_path) {
            const img = document.createElement('img');
            img.src = `file:///${item.image_path.replace(/\\/g, '/')}`;
            img.className = 'w-16 h-16 object-cover rounded';
            img.onerror = function() {
                this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
            };
            previewCell.appendChild(img);
        } else {
            previewCell.innerHTML = '<div class="w-16 h-16 bg-base-200 rounded flex items-center justify-center text-xs">无图片</div>';
        }

        // ID
        const idCell = document.createElement('td');
        idCell.innerHTML = `<span class="font-mono text-sm">${item.image_id || 'N/A'}</span>`;

        // 用户
        const userCell = document.createElement('td');
        userCell.textContent = item.username || 'Unknown';

        // 模型
        const modelCell = document.createElement('td');
        const modelName = item.model_name || 'Unknown';
        modelCell.innerHTML = `<span class="text-sm" title="${modelName}">${modelName.length > 20 ? modelName.substring(0, 20) + '...' : modelName}</span>`;

        // 尺寸
        const sizeCell = document.createElement('td');
        if (item.size_width && item.size_height) {
            sizeCell.textContent = `${item.size_width}×${item.size_height}`;
        } else {
            sizeCell.textContent = 'N/A';
        }

        // 分类
        const categoryCell = document.createElement('td');
        categoryCell.innerHTML = `<span class="badge badge-outline">${item.category || 'Unknown'}</span>`;

        // 操作
        const actionCell = document.createElement('td');
        actionCell.innerHTML = `
            <div class="flex gap-1">
                <button class="btn btn-xs btn-primary" onclick="viewMetaDetails('${item.image_id}')">详情</button>
                ${item.image_exists ? `<button class="btn btn-xs btn-secondary" onclick="openImageLocation('${item.image_path}')">打开</button>` : ''}
            </div>
        `;

        row.appendChild(previewCell);
        row.appendChild(idCell);
        row.appendChild(userCell);
        row.appendChild(modelCell);
        row.appendChild(sizeCell);
        row.appendChild(categoryCell);
        row.appendChild(actionCell);

        tbody.appendChild(row);
    });

    // 更新分页
    const totalPages = Math.ceil(currentMetaData.length / metaItemsPerPage);
    if (totalPages > 1) {
        pagination.style.display = 'flex';
        document.getElementById('currentMetaPage').textContent = `${currentMetaPage} / ${totalPages}`;
    } else {
        pagination.style.display = 'none';
    }
}

// 清除筛选条件
function clearMetaFilters() {
    document.getElementById('metaSearchQuery').value = '';
    document.getElementById('metaCategoryFilter').value = 'all';
    document.getElementById('metaUserFilter').value = '';
    document.getElementById('metaNsfwFilter').value = 'all';
}

// 分页功能
function prevMetaPage() {
    if (currentMetaPage > 1) {
        currentMetaPage--;
        renderMetaSearchResults();
    }
}

function nextMetaPage() {
    const totalPages = Math.ceil(currentMetaData.length / metaItemsPerPage);
    if (currentMetaPage < totalPages) {
        currentMetaPage++;
        renderMetaSearchResults();
    }
}

// 查看元数据详情
function viewMetaDetails(imageId) {
    const item = currentMetaData.find(item => item.image_id === imageId);
    if (!item) {
        alert('找不到该图片的元数据');
        return;
    }

    // 创建详情模态框内容
    const modalContent = `
        <div class="modal modal-open">
            <div class="modal-box w-11/12 max-w-4xl">
                <h3 class="font-bold text-lg mb-4">元数据详情 - ${imageId}</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-semibold mb-2">基本信息</h4>
                        <div class="space-y-1 text-sm">
                            <p><strong>ID:</strong> ${item.image_id}</p>
                            <p><strong>用户:</strong> ${item.username || 'Unknown'}</p>
                            <p><strong>模型:</strong> ${item.model_name || 'Unknown'}</p>
                            <p><strong>尺寸:</strong> ${item.size_width && item.size_height ? `${item.size_width}×${item.size_height}` : 'N/A'}</p>
                            <p><strong>分类:</strong> ${item.category || 'Unknown'}</p>
                            <p><strong>NSFW:</strong> ${item.nsfw ? '是' : '否'}</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">生成参数</h4>
                        <div class="space-y-1 text-sm">
                            <p><strong>采样器:</strong> ${item.sampler || 'N/A'}</p>
                            <p><strong>步数:</strong> ${item.steps || 'N/A'}</p>
                            <p><strong>CFG:</strong> ${item.cfg_scale || 'N/A'}</p>
                            <p><strong>种子:</strong> ${item.seed || 'N/A'}</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <h4 class="font-semibold mb-2">提示词</h4>
                    <textarea class="textarea textarea-bordered w-full h-24" readonly>${item.prompt || '无提示词'}</textarea>
                </div>
                <div class="mt-4">
                    <h4 class="font-semibold mb-2">负面提示词</h4>
                    <textarea class="textarea textarea-bordered w-full h-20" readonly>${item.negative_prompt || '无负面提示词'}</textarea>
                </div>
                <div class="modal-action">
                    <button class="btn" onclick="closeMetaModal()">关闭</button>
                </div>
            </div>
        </div>
    `;

    // 添加模态框到页面
    const modalDiv = document.createElement('div');
    modalDiv.id = 'metaDetailModal';
    modalDiv.innerHTML = modalContent;
    document.body.appendChild(modalDiv);
}

// 关闭元数据详情模态框
function closeMetaModal() {
    const modal = document.getElementById('metaDetailModal');
    if (modal) {
        modal.remove();
    }
}

// 打开图片位置
async function openImageLocation(imagePath) {
    try {
        await window.pywebview.api.open_file_location(imagePath);
    } catch (error) {
        console.error('打开文件位置失败:', error);
        alert('打开文件位置失败: ' + error.message);
    }
}