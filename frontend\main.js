document.addEventListener('DOMContentLoaded', function() {
    console.log('前端JS已加载，Hello from main.js!');

    // 初始化主题
    initializeTheme();

    // 图片展示相关元素
    const imageSearchBtn = document.getElementById('imageSearchBtn');
    const imageDownloadBtn = document.getElementById('imageDownloadBtn');
    const imageDirectoryBtn = document.getElementById('imageDirectoryBtn');
    const imageGallery = document.getElementById('imageGallery');
    const imageProgress = document.getElementById('imageProgress');
    const imageProgressText = document.getElementById('imageProgressText');

    // 元数据管理相关元素
    const metaSearchBtn = document.getElementById('metaSearchBtn');
    const metaClearBtn = document.getElementById('metaClearBtn');
    const metaRefreshBtn = document.getElementById('metaRefreshBtn');
    const metaResultTable = document.getElementById('metaResultTable');
    const metaResultInfo = document.getElementById('metaResultInfo');

    const apiKeyInput = document.getElementById('apiKeyInput');
    const saveApiKeyBtn = document.getElementById('saveApiKeyBtn');

    // 图片展示功能
    if (imageSearchBtn) {
        imageSearchBtn.addEventListener('click', async function() {
            const username = document.getElementById('imageUsername').value.trim();
            const modelId = document.getElementById('imageModelId').value.trim();
            const maxCount = document.getElementById('imageMaxCount').value;

            if (!username && !modelId) {
                alert('请输入用户名或模型ID');
                return;
            }

            imageProgress.classList.remove('hidden');
            imageProgressText.textContent = '正在搜索图片...';

            try {
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.get_images(
                        username || undefined,
                        modelId ? parseInt(modelId) : undefined,
                        undefined,
                        parseInt(maxCount),
                        undefined
                    );

                    if (result && result.items && result.items.length > 0) {
                        renderImageGallery(result.items);
                        imageProgressText.textContent = `找到 ${result.items.length} 张图片`;
                    } else {
                        imageGallery.innerHTML = '<div class="text-center text-base-content/50 col-span-full py-12"><p>未找到图片</p></div>';
                        imageProgressText.textContent = '未找到图片';
                    }
                } else {
                    alert('API 不可用');
                }
            } catch (error) {
                console.error('搜索图片失败:', error);
                imageProgressText.textContent = '搜索失败: ' + error.message;
            }

            setTimeout(() => {
                imageProgress.classList.add('hidden');
            }, 3000);
        });
    }

    // 图片下载功能
    if (imageDownloadBtn) {
        imageDownloadBtn.addEventListener('click', async function() {
            const username = document.getElementById('imageUsername').value.trim();
            const modelId = document.getElementById('imageModelId').value.trim();
            const maxCount = document.getElementById('imageMaxCount').value;

            if (!username && !modelId) {
                alert('请输入用户名或模型ID');
                return;
            }

            imageProgress.classList.remove('hidden');
            imageProgressText.textContent = '开始批量下载...';

            try {
                if (window.pywebview && window.pywebview.api) {
                    await window.pywebview.api.batch_download(
                        'downloads',
                        username || undefined,
                        modelId ? parseInt(modelId) : undefined,
                        undefined,
                        20,
                        parseInt(maxCount)
                    );
                    imageProgressText.textContent = '下载完成！';
                } else {
                    alert('API 不可用');
                }
            } catch (error) {
                console.error('批量下载失败:', error);
                imageProgressText.textContent = '下载失败: ' + error.message;
            }

            setTimeout(() => {
                imageProgress.classList.add('hidden');
            }, 3000);
        });
    }

    // 打开目录功能
    if (imageDirectoryBtn) {
        imageDirectoryBtn.addEventListener('click', async function() {
            try {
                if (window.pywebview && window.pywebview.api) {
                    await window.pywebview.api.open_download_directory();
                } else {
                    alert('API 不可用');
                }
            } catch (error) {
                console.error('打开目录失败:', error);
                alert('打开目录失败: ' + error.message);
            }
        });
    }

    // 图片渲染函数
    function renderImageGallery(items) {
        if (!imageGallery) return;

        imageGallery.innerHTML = '';

        items.forEach(item => {
            const card = document.createElement('div');
            card.className = 'card bg-base-100 shadow-lg';

            card.innerHTML = `
                <figure class="px-2 pt-2">
                    <img src="${item.url}" alt="Image ${item.id}" class="rounded-xl w-full h-48 object-cover"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='">
                </figure>
                <div class="card-body p-4">
                    <p class="text-xs text-base-content/70">ID: ${item.id}</p>
                    <p class="text-xs text-base-content/90 line-clamp-2" title="${item.meta?.prompt || '无提示词'}">
                        ${item.meta?.prompt ? (item.meta.prompt.length > 50 ? item.meta.prompt.slice(0, 50) + '...' : item.meta.prompt) : '无提示词'}
                    </p>
                    <div class="card-actions justify-center mt-2">
                        <button class="btn btn-primary btn-sm" onclick="downloadSingleImage('${item.id}')">下载</button>
                    </div>
                </div>
            `;

            imageGallery.appendChild(card);
        });
    }

    // 单张图片下载函数
    window.downloadSingleImage = async function(imageId) {
        try {
            if (window.pywebview && window.pywebview.api) {
                const result = await window.pywebview.api.download_single_image(imageId, 'downloads');
                if (result && result.success) {
                    alert('图片下载成功！');
                } else {
                    alert('图片下载失败');
                }
            } else {
                alert('API 不可用');
            }
        } catch (error) {
            console.error('下载图片失败:', error);
            alert('下载图片失败: ' + error.message);
        }
    };

    // 元数据管理功能
    if (metaSearchBtn) {
        metaSearchBtn.addEventListener('click', async function() {
            const searchQuery = document.getElementById('metaSearchInput').value.trim();
            const userFilter = document.getElementById('metaUserInput').value.trim();

            metaResultInfo.textContent = '正在搜索...';

            try {
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.search_local_metadata(
                        searchQuery || '',
                        'all',
                        userFilter || '',
                        '',
                        'all'
                    );

                    if (result && result.metadata) {
                        renderMetaResults(result.metadata);
                        metaResultInfo.textContent = `找到 ${result.total_results || result.metadata.length} 条结果`;
                    } else {
                        metaResultTable.innerHTML = '<tr><td colspan="6" class="text-center py-8">未找到结果</td></tr>';
                        metaResultInfo.textContent = '未找到结果';
                    }
                } else {
                    alert('API 不可用');
                }
            } catch (error) {
                console.error('搜索元数据失败:', error);
                metaResultInfo.textContent = '搜索失败: ' + error.message;
            }
        });
    }

    if (metaClearBtn) {
        metaClearBtn.addEventListener('click', function() {
            document.getElementById('metaSearchInput').value = '';
            document.getElementById('metaUserInput').value = '';
            metaResultTable.innerHTML = '<tr><td colspan="6" class="text-center text-base-content/50 py-12"><p>请先进行搜索以查看本地元数据</p></td></tr>';
            metaResultInfo.textContent = '点击"搜索"开始浏览本地元数据';
        });
    }

    if (metaRefreshBtn) {
        metaRefreshBtn.addEventListener('click', async function() {
            try {
                if (window.pywebview && window.pywebview.api) {
                    const stats = await window.pywebview.api.get_metadata_statistics();

                    document.getElementById('metaTotalImages').textContent = stats.total_images || 0;
                    document.getElementById('metaTotalCategories').textContent = Object.keys(stats.categories || {}).length;
                    document.getElementById('metaTotalUsers').textContent = Object.keys(stats.users || {}).length;
                    document.getElementById('metaTotalModels').textContent = Object.keys(stats.models || {}).length;

                    alert('统计信息已刷新');
                } else {
                    alert('API 不可用');
                }
            } catch (error) {
                console.error('刷新统计失败:', error);
                alert('刷新统计失败: ' + error.message);
            }
        });
    }

    // 元数据结果渲染函数
    function renderMetaResults(metadata) {
        if (!metaResultTable) return;

        metaResultTable.innerHTML = '';

        if (!metadata || metadata.length === 0) {
            metaResultTable.innerHTML = '<tr><td colspan="6" class="text-center py-8">暂无数据</td></tr>';
            return;
        }

        metadata.slice(0, 20).forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="text-center">
                    ${item.image_exists ?
                        `<img src="file:///${item.image_path?.replace(/\\/g, '/')}" class="w-12 h-12 object-cover rounded" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIGZpbGw9IiNmM2Y0ZjYiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxMCIgZmlsbD0iIzljYTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOWbvjwvdGV4dD48L3N2Zz4='">`
                        : '<div class="w-12 h-12 bg-base-200 rounded flex items-center justify-center text-xs">无图</div>'
                    }
                </td>
                <td class="text-sm">${item.image_id || 'N/A'}</td>
                <td class="text-sm">${item.username || 'Unknown'}</td>
                <td class="text-sm">${item.model_name || 'Unknown'}</td>
                <td class="text-sm">${item.size_width && item.size_height ? `${item.size_width}×${item.size_height}` : 'N/A'}</td>
                <td class="text-center">
                    <button class="btn btn-xs btn-outline" onclick="viewMetaDetail('${item.image_id}')">详情</button>
                </td>
            `;
            metaResultTable.appendChild(row);
        });
    }

    // 查看元数据详情
    window.viewMetaDetail = function(imageId) {
        alert(`查看图片 ${imageId} 的详细信息功能待实现`);
    };

    if (saveApiKeyBtn) {
        saveApiKeyBtn.addEventListener('click', async function() {
            const key = apiKeyInput.value.trim();
            if (!key) {
                alert('请输入API Key');
                return;
            }
            try {
                if (window.pywebview && window.pywebview.api) {
                    await window.pywebview.api.set_api_key(key);
                    alert('API Key已保存！');
                } else {
                    alert('pywebview API 不可用');
                }
            } catch (err) {
                alert('保存API Key失败：' + err);
            }
        });
    }

    // 侧边栏切换逻辑
    const btnSettings = document.getElementById('btnSettings');
    const btnImages = document.getElementById('btnImages');
    const btnMeta = document.getElementById('btnMeta');
    const settingsPanel = document.getElementById('settingsPanel');
    const imagesPanel = document.getElementById('imagesPanel');
    const metaPanel = document.getElementById('metaPanel');

    // 默认显示程序设置面板，隐藏其他面板
    if(settingsPanel) settingsPanel.classList.add('active');
    if(imagesPanel) imagesPanel.classList.remove('active');
    if(metaPanel) metaPanel.classList.remove('active');
    if(btnSettings) btnSettings.classList.add('btn-primary'); // Set initial active button
    if(btnImages) btnImages.classList.remove('btn-primary');
    if(btnImages) btnImages.classList.add('btn-ghost');
    if(btnMeta) btnMeta.classList.remove('btn-primary');
    if(btnMeta) btnMeta.classList.add('btn-ghost');


    function setActivePanel(panelId) {
        const panels = {
            settings: settingsPanel,
            images: imagesPanel,
            meta: metaPanel
        };
        const buttons = {
            settings: btnSettings,
            images: btnImages,
            meta: btnMeta
        };

        // Hide all panels and reset button styles
        for (const key in panels) {
            if (panels[key]) panels[key].classList.remove('active');
            if (buttons[key]) {
                buttons[key].classList.remove('btn-primary');
                buttons[key].classList.add('btn-ghost');
            }
        }

        // Show the selected panel and set button style
        if (panels[panelId]) panels[panelId].classList.add('active');
        if (buttons[panelId]) {
            buttons[panelId].classList.remove('btn-ghost');
            buttons[panelId].classList.add('btn-primary');
        }
    }

    if(btnSettings) btnSettings.addEventListener('click', function() { setActivePanel('settings'); });
    if(btnImages) btnImages.addEventListener('click', function() { setActivePanel('images'); });
    if(btnMeta) btnMeta.addEventListener('click', function() {
        setActivePanel('meta');
        // 切换到元数据面板时自动刷新统计
        refreshMetadata();
    });

    // Ensure initial state is correct
    setActivePanel('settings'); // 默认显示程序设置面板
});

// 打开下载目录
async function showDirectoryStructure() {
    try {
        const result = await pywebview.api.open_download_directory();
        console.log('打开目录结果:', result);

        if (result.success) {
            console.log('已打开下载目录:', result.path);
            // 可选：显示一个简短的提示
            const progressText = document.getElementById('progressText');
            if (progressText) {
                const originalText = progressText.textContent;
                progressText.textContent = '已打开下载目录';
                setTimeout(() => {
                    progressText.textContent = originalText;
                }, 2000);
            }
        } else {
            alert('打开目录失败: ' + result.error);
        }
    } catch (error) {
        console.error('打开目录失败:', error);
        alert('打开目录失败: ' + error.message);
    }
}

// 监听主题控制器变化
function initializeThemeController() {
    // 获取所有主题控制器
    const themeControllers = document.querySelectorAll('.theme-controller');

    // 为每个主题控制器添加事件监听
    themeControllers.forEach(controller => {
        controller.addEventListener('change', function() {
            if (this.checked) {
                const themeName = this.value;
                console.log('主题已切换到:', themeName);

                // 更新侧边栏的明暗切换开关状态
                updateSidebarToggle(themeName);

                // 保存主题到本地存储
                localStorage.setItem('selectedTheme', themeName);
            }
        });
    });

    // 恢复保存的主题
    const savedTheme = localStorage.getItem('selectedTheme') || 'light';
    setTheme(savedTheme);
}

// 设置主题
function setTheme(themeName) {
    // 设置data-theme属性
    document.documentElement.setAttribute('data-theme', themeName);

    // 选中对应的radio按钮
    const themeController = document.querySelector(`input[name="theme-dropdown"][value="${themeName}"]`);
    if (themeController) {
        themeController.checked = true;
    }

    // 更新侧边栏切换开关
    updateSidebarToggle(themeName);
}

// 更新侧边栏明暗切换开关状态
function updateSidebarToggle(themeName) {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const darkThemes = ['dark', 'night', 'black', 'dracula', 'halloween', 'forest', 'luxury', 'business', 'coffee', 'dim'];
        themeToggle.checked = darkThemes.includes(themeName);
    }
}

// 明暗主题切换函数（侧边栏开关）
function toggleDarkMode(toggle) {
    const isDark = toggle.checked;
    const newTheme = isDark ? 'dark' : 'light';
    setTheme(newTheme);

    // 保存主题到本地存储
    localStorage.setItem('selectedTheme', newTheme);
}

// 页面加载时初始化主题
function initializeTheme() {
    initializeThemeController();
}



// 打开下载目录
async function showDirectoryStructure() {
    try {
        const result = await pywebview.api.open_download_directory();
        console.log('打开目录结果:', result);

        if (result.success) {
            console.log('已打开下载目录:', result.path);
        } else {
            alert('打开目录失败: ' + result.error);
        }
    } catch (error) {
        console.error('打开目录失败:', error);
        alert('打开目录失败: ' + error.message);
    }
}





    console.log('图片展示与元数据管理功能已初始化');
}